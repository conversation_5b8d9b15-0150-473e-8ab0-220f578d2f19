<svg width="1200" height="500" xmlns="http://www.w3.org/2000/svg">
    <!-- 
      SVG Technical Fix:
      - Escaped the ampersand character ('&' to '&amp;') in text elements to fix the XML parsing error.
      - Removed the <style> block to improve compatibility with various vector editing tools.
      - Converted all CSS classes to inline presentation attributes (e.g., fill, stroke, font-size) on each element.
      - Changed rgba() color to hex + opacity for broader support.
      - This should resolve import/rendering issues in most editors.
    -->
    <defs>
        <marker id="arrow" viewBox="0 0 10 10" refX="5" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
          <path d="M 0 0 L 10 5 L 0 10 z" fill="#000000" />
        </marker>
    </defs>

    <!-- 输入 -->
    <g id="input-stream">
        <rect x="20" y="220" width="150" height="60" fill="#ffffff" stroke="#000000" stroke-width="1" rx="5" ry="5" />
        <text x="95" y="245" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="15" font-weight="bold" text-anchor="middle" fill="#000000">标准化事件流</text>
        <text x="95" y="265" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="12" text-anchor="middle" fill="#333333">From Flink</text>
    </g>

    <!-- Drools 引擎 (大框) -->
    <rect x="200" y="30" width="700" height="440" fill="#f5f5f5" fill-opacity="0.5" stroke="#888888" stroke-width="1" stroke-dasharray="5,5" rx="8" ry="8" />
    <text x="550" y="20" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#444444">Drools 规则引擎</text>

    <!-- 规则动态管理 (UI部分) -->
    <g id="rule-management-ui">
        <rect x="220" y="50" width="660" height="150" fill="#ffffff" stroke="#000000" stroke-width="1" rx="5" ry="5" />
        <text x="550" y="70" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="15" font-weight="bold" text-anchor="middle" fill="#000000">规则动态管理界面 (KieFileSystem)</text>
        
        <!-- 规则编辑 -->
        <rect x="230" y="85" width="200" height="105" fill="#f9f9f9" stroke="#ccc" />
        <text x="330" y="100" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="12" text-anchor="middle" fill="#333333" font-weight="bold">1. 规则编辑</text>
        <text x="235" y="118" font-family="Courier New, monospace" font-size="11" text-anchor="start" fill="#1a1a1a">rule "军事管理区违规采集"</text>
        <text x="245" y="133" font-family="Courier New, monospace" font-size="11" text-anchor="start" fill="#1a1a1a">when</text>
        <text x="255" y="148" font-family="Courier New, monospace" font-size="11" text-anchor="start" fill="#1a1a1a">$v: Vehicle(area == "军事管理区")</text>
        <text x="245" y="163" font-family="Courier New, monospace" font-size="11" text-anchor="start" fill="#1a1a1a">then</text>
        <text x="255" y="178" font-family="Courier New, monospace" font-size="11" text-anchor="start" fill="#1a1a1a">insert(new Risk("RT-001"));</text>
        
        <!-- 版本管理 -->
        <rect x="440" y="85" width="210" height="105" fill="#f9f9f9" stroke="#ccc" />
        <text x="545" y="100" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="12" text-anchor="middle" fill="#333333" font-weight="bold">2. 版本管理 (回滚/审计)</text>
        <text x="450" y="120" font-family="Courier New, monospace" font-size="11" text-anchor="start" fill="#1a1a1a">v1.2 (Draft) - by user_A @ 07-23</text>
        <text x="450" y="140" font-family="Courier New, monospace" font-size="11" text-anchor="start" fill="#008000">v1.1 (Active) - by admin @ 07-20</text>
        <text x="450" y="160" font-family="Courier New, monospace" font-size="11" text-anchor="start" fill="#1a1a1a">v1.0 (Archived) - by admin @ 07-15</text>
        <rect x="560" y="165" width="80" height="20" rx="3" fill="#e0e0e0" />
        <text x="600" y="179" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="12" text-anchor="middle" fill="#333333">回滚至 v1.0</text>

        <!-- 部署 -->
        <rect x="660" y="85" width="210" height="105" fill="#f9f9f9" stroke="#ccc" />
        <text x="765" y="100" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="12" text-anchor="middle" fill="#333333" font-weight="bold">3. 测试 &amp; 部署</text>
        <rect x="670" y="120" width="190" height="25" rx="3" fill="#d3e8d3" />
        <text x="765" y="137" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="12" text-anchor="middle" fill="#333333">运行单元测试</text>
        <rect x="670" y="155" width="190" height="25" rx="3" fill="#cde7f5" />
        <text x="765" y="172" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="12" text-anchor="middle" fill="#333333">热部署到生产环境</text>
    </g>
    
    <!-- 箭头从管理指向执行核心 -->
    <path d="M 550 200 Q 550 220 550 240" fill="none" stroke="#000000" stroke-width="1.5" marker-end="url(#arrow)" />
    <text x="585" y="225" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="12" text-anchor="middle" fill="#333333">编译 &amp; 加载</text>

    <!-- 执行核心 -->
    <g id="execution-core">
        <rect x="220" y="240" width="660" height="220" fill="#ffffff" stroke="#000000" stroke-width="1" rx="5" ry="5" />
        <text x="550" y="260" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="15" font-weight="bold" text-anchor="middle" fill="#000000">执行核心 (RETE 算法)</text>
        
        <text x="300" y="290" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="12" text-anchor="middle" fill="#333333">Working Memory</text>
        <text x="300" y="305" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="12" text-anchor="middle" fill="#333333">(事实数据)</text>
        <path d="M 300 315 L 300 350" fill="none" stroke="#000000" stroke-width="1.5" marker-end="url(#arrow)" />

        <rect x="250" y="350" width="100" height="40" fill="#ffffff" stroke="#000000" stroke-width="1" rx="5" ry="5" />
        <text x="300" y="375" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="15" font-weight="bold" text-anchor="middle" fill="#000000">Alpha Network</text>
        <text x="300" y="365" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="10" text-anchor="middle" fill="#333333">(单条件匹配)</text>

        <path d="M 350 370 L 450 370" fill="none" stroke="#000000" stroke-width="1.5" marker-end="url(#arrow)" />

        <rect x="450" y="350" width="100" height="40" fill="#ffffff" stroke="#000000" stroke-width="1" rx="5" ry="5" />
        <text x="500" y="375" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="15" font-weight="bold" text-anchor="middle" fill="#000000">Beta Network</text>
        <text x="500" y="365" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="10" text-anchor="middle" fill="#333333">(多条件关联)</text>

        <path d="M 550 370 L 650 370" fill="none" stroke="#000000" stroke-width="1.5" marker-end="url(#arrow)" />

        <rect x="650" y="350" width="100" height="40" fill="#ffffff" stroke="#000000" stroke-width="1" rx="5" ry="5" />
        <text x="700" y="375" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="15" font-weight="bold" text-anchor="middle" fill="#000000">Agenda</text>
        <text x="700" y="365" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="10" text-anchor="middle" fill="#333333">(待执行规则)</text>

        <path d="M 700 390 L 700 425" fill="none" stroke="#000000" stroke-width="1.5" marker-end="url(#arrow)" />
        <rect x="650" y="425" width="100" height="30" fill="#ffffff" stroke="#000000" stroke-width="1" rx="5" ry="5" />
        <text x="700" y="445" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="15" font-weight="bold" text-anchor="middle" fill="#000000">执行</text>
    </g>

    <!-- 输出 -->
    <g id="output-stream">
        <rect x="930" y="220" width="150" height="60" fill="#ffffff" stroke="#000000" stroke-width="1" rx="5" ry="5" />
        <text x="1005" y="245" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="15" font-weight="bold" text-anchor="middle" fill="#000000">风险事件</text>
        <text x="1005" y="265" font-family="Microsoft YaHei, SimSun, sans-serif" font-size="12" text-anchor="middle" fill="#333333">触发预警/处置</text>
    </g>

    <!-- 主流程连线 -->
    <polyline points="170,250 220,250 220,300 250,300" fill="none" stroke="#000000" stroke-width="1.5" marker-end="url(#arrow)" />
    <polyline points="750,440 880,440 880,250 930,250" fill="none" stroke="#000000" stroke-width="1.5" marker-end="url(#arrow)" />
</svg>
