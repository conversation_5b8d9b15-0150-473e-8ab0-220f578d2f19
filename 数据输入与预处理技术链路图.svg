<svg width="1100" height="400" xmlns="http://www.w3.org/2000/svg">
    <!-- 定义样式 -->
    <style>
        .rect-style {
            fill: #ffffff;
            stroke: #000000;
            stroke-width: 1;
            rx: 5; /* 圆角 */
            ry: 5;
        }
        .text-style {
            font-family: "Microsoft YaHei", "SimSun", sans-serif;
            font-size: 15px;
            font-weight: bold;
            text-anchor: middle;
            fill: #000000;
        }
        .line-style {
            fill: none;
            stroke: #000000;
            stroke-width: 1.5;
            marker-end: url(#arrow);
        }
        .sub-text-style {
            font-family: "Microsoft YaHei", "SimSun", sans-serif;
            font-size: 12px;
            text-anchor: middle;
            fill: #333333;
        }
        .group-box {
            fill: none;
            stroke: #888888;
            stroke-width: 1;
            stroke-dasharray: 5,5;
            rx: 8;
            ry: 8;
        }
        .group-text-style {
            font-family: "Microsoft YaHei", "SimSun", sans-serif;
            font-size: 14px;
            font-weight: bold;
            text-anchor: middle;
            fill: #444444;
        }
    </style>

    <!-- 定义箭头 -->
    <defs>
        <marker id="arrow" viewBox="0 0 10 10" refX="5" refY="5"
            markerWidth="6" markerHeight="6"
            orient="auto-start-reverse">
          <path d="M 0 0 L 10 5 L 0 10 z" />
        </marker>
    </defs>

    <!-- 元素组 -->
    <!-- 数据源 -->
    <g id="data-source">
        <rect x="20" y="170" width="140" height="60" class="rect-style" />
        <text x="90" y="195" class="text-style">原始数据流</text>
        <text x="90" y="215" class="sub-text-style">车端/企业端二进制数据</text>
    </g>

    <!-- 数据接入网关 (包含协议解析) -->
    <g id="ingestion-gateway">
        <rect x="200" y="170" width="160" height="90" class="rect-style" />
        <text x="280" y="195" class="text-style">协议解析 (Netty)</text>
        <text x="280" y="218" class="sub-text-style">高性能协议解析器</text>
        <text x="280" y="235" class="sub-text-style">Zero-Copy 技术</text>
        <text x="280" y="252" class="sub-text-style">ByteBuf 对象池</text>
    </g>
    
    <!-- Flink 实时处理管道 (大框) -->
    <rect x="400" y="50" width="480" height="300" class="group-box" />
    <text x="640" y="35" class="group-text-style">实时数据处理管道 (Flink DataStream API)</text>
    
    <g id="stream-processing">
        <!-- 数据预处理 -->
        <rect x="420" y="170" width="160" height="90" class="rect-style" />
        <text x="500" y="195" class="text-style">数据预处理</text>
        <text x="500" y="218" class="sub-text-style">数据清洗</text>
        <text x="500" y="235" class="sub-text-style">格式转换</text>
        <text x="500" y="252" class="sub-text-style">质量校验</text>

        <!-- 实时分析与性能优化 -->
        <rect x="620" y="170" width="240" height="90" class="rect-style" />
        <text x="740" y="195" class="text-style">实时分析与性能优化</text>
        <text x="740" y="218" class="sub-text-style">时间窗口聚合 (识别异常行为)</text>
        <text x="740" y="235" class="sub-text-style">背压处理 (流量控制)</text>
        <text x="740" y="252" class="sub-text-style">并行化处理 (多核利用)</text>
    </g>

    <!-- 标准化输出 -->
    <g id="output">
        <rect x="920" y="170" width="150" height="60" class="rect-style" />
        <text x="995" y="195" class="text-style">标准化事件流</text>
        <text x="995" y="215" class="sub-text-style">To 风险识别引擎</text>
    </g>

    <!-- 连线 -->
    <polyline points="160,200 200,200" class="line-style" />
    <polyline points="360,200 420,200" class="line-style" />
    <polyline points="580,200 620,200" class="line-style" />
    <polyline points="860,200 920,200" class="line-style" />

</svg>
