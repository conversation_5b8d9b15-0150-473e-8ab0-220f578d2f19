<svg width="800" height="1200" viewBox="0 0 800 1200" xmlns="http://www.w3.org/2000/svg" font-family="Arial, sans-serif" font-size="14px">
    <style>
        .title-box { fill: #FFFFFF; stroke: #000000; stroke-width: 1.5; }
        .lifeline { stroke: #000000; stroke-width: 1; stroke-dasharray: 5,5; }
        .arrow { stroke: #000000; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); }
        .resp-arrow { stroke: #000000; stroke-width: 1.5; fill: none; stroke-dasharray: 4,4; marker-end: url(#arrowhead); }
        .text { fill: #000000; text-anchor: middle; dominant-baseline: middle; }
        .section-box { fill: none; stroke: #000000; stroke-width: 1; }
        .section-text { fill: #333333; font-size: 14px; font-style: italic; text-anchor: middle; }
    </style>
    <defs>
        <marker id="arrowhead" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
            <path d="M 0 0 L 10 5 L 0 10 z" fill="#000000" />
        </marker>
    </defs>

    <!-- 参与者 -->
    <rect x="50" y="20" width="300" height="50" class="title-box" rx="5" />
    <text x="200" y="45" class="text" font-size="16px" font-weight="bold">企业端平台 (客户端)</text>
    <line x1="200" y1="70" x2="200" y2="1180" class="lifeline" />

    <rect x="450" y="20" width="300" height="50" class="title-box" rx="5" />
    <text x="600" y="45" class="text" font-size="16px" font-weight="bold">政府监测平台 (服务端)</text>
    <line x1="600" y1="70" x2="600" y2="1180" class="lifeline" />

    <!-- 流程 -->
    <!-- Section 1: 鉴权与登入 -->
    <rect x="20" y="90" width="760" height="150" class="section-box" rx="10" />
    <text x="400" y="110" class="section-text">1. 鉴权与登入流程</text>
    <path d="M 200 140 L 600 140" class="arrow" />
    <text x="400" y="130" class="text">1. TCP 连接请求</text>
    <path d="M 600 170 L 200 170" class="resp-arrow" />
    <text x="400" y="160" class="text">2. TCP 连接建立</text>
    <path d="M 200 200 L 600 200" class="arrow" />
    <text x="400" y="190" class="text">3. 平台登入 (0x10)</text>
    <path d="M 600 230 L 200 230" class="resp-arrow" />
    <text x="400" y="220" class="text">4. 平台登入应答 (0x90)</text>

    <!-- Section 2: 常规数据交互 -->
    <rect x="20" y="250" width="760" height="210" class="section-box" rx="10" />
    <text x="400" y="270" class="section-text">2. 常规数据交互</text>
    <path d="M 200 300 L 600 300" class="arrow" />
    <text x="400" y="290" class="text">5. 实时信息上报 (0x20)</text>
    <path d="M 600 330 L 200 330" class="resp-arrow" />
    <text x="400" y="320" class="text">6. 平台通用应答 (0x81)</text>
    <path d="M 200 360 L 600 360" class="arrow" />
    <text x="400" y="350" class="text">7. 事件数据上报 (0x30)</text>
    <path d="M 600 390 L 200 390" class="resp-arrow" />
    <text x="400" y="380" class="text">8. 平台通用应答 (0x81)</text>
    <path d="M 200 420 L 600 420" class="arrow" />
    <text x="400" y="410" class="text">9. 客户端心跳 (0x02)</text>
    <path d="M 600 450 L 200 450" class="resp-arrow" />
    <text x="400" y="440" class="text">10. 平台通用应答 (0x81)</text>
    
    <!-- Section 3: 数据补发流程 -->
    <rect x="20" y="470" width="760" height="120" class="section-box" rx="10" />
    <text x="400" y="490" class="section-text">3. 数据补发流程 (通信中断后)</text>
    <path d="M 200 520 L 600 520" class="arrow" />
    <text x="400" y="510" class="text">11. 补发信息上报 (0x21, 可分包)</text>
    <path d="M 600 550 L 200 550" class="resp-arrow" />
    <text x="400" y="540" class="text">12. 平台通用应答 (0x81)</text>
    <text x="400" y="575" class="text" font-size="12px" fill="#666">(对最后一包的应答)</text>

    <!-- Section 4: 服务端控制流程 -->
    <rect x="20" y="600" width="760" height="120" class="section-box" rx="10" />
    <text x="400" y="620" class="section-text">4. 服务端控制流程</text>
    <path d="M 600 650 L 200 650" class="arrow" />
    <text x="400" y="640" class="text">13. 控制指令 (0x83, 如数据查询)</text>
    <path d="M 200 680 L 600 680" class="resp-arrow" />
    <text x="400" y="670" class="text">14. 客户端通用应答 (0x01)</text>
    <text x="400" y="705" class="text" font-size="12px" fill="#666">(若查询数据，则后续通过FTP/HTTP等方式传输)</text>
    
    <!-- Section 5: 密钥交换 -->
    <rect x="20" y="730" width="760" height="150" class="section-box" rx="10" />
    <text x="400" y="750" class="section-text">5. 密钥交换流程 (可选)</text>
    <path d="M 600 780 L 200 780" class="arrow" />
    <text x="400" y="770" class="text">15. 平台密钥交换 (0xF0, 下发公钥)</text>
    <path d="M 200 810 L 600 810" class="resp-arrow" />
    <text x="400" y="800" class="text">16. 客户端密钥交换 (0x70, 上报公钥)</text>
    <path d="M 200 840 L 600 840" class="arrow" />
    <text x="400" y="830" class="text">17. 客户端密钥交换 (0x70, 上报加密后的会话密钥)</text>
    <path d="M 600 870 L 200 870" class="resp-arrow" />
    <text x="400" y="860" class="text">18. 平台通用应答 (0x81)</text>

    <!-- Section 6: 登出 -->
    <rect x="20" y="890" width="760" height="120" class="section-box" rx="10" />
    <text x="400" y="910" class="section-text">6. 登出流程</text>
    <path d="M 200 940 L 600 940" class="arrow" />
    <text x="400" y="930" class="text">19. 平台登出 (0x03)</text>
    <path d="M 600 970 L 200 970" class="resp-arrow" />
    <text x="400" y="960" class="text">20. TCP 连接断开</text>

</svg>
