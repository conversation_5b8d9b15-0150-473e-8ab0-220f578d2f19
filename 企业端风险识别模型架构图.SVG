<svg width="1300" height="600" xmlns="http://www.w3.org/2000/svg">
    <!-- SVG by Gemini -->
    <defs>
        <marker id="arrow-risk-enterprise" viewBox="0 0 10 10" refX="5" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
          <path d="M 0 0 L 10 5 L 0 10 z" fill="#000000" />
        </marker>
        <filter id="shadow-enterprise" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
            <feOffset dx="2" dy="2" result="offsetblur"/>
            <feMerge>
                <feMergeNode/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>

    <style>
        .stage-box { fill: #f8f9fa; stroke: #6c757d; stroke-width: 1.5; rx: 8; ry: 8; }
        .stage-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; fill: #343a40; }
        .risk-item-box { fill: #ffffff; stroke: #ced4da; stroke-width: 1; rx: 4; ry: 4; }
        .risk-item-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 14px; text-anchor: middle; fill: #212529; }
        .tech-text { font-family: 'Courier New', monospace; font-size: 11px; text-anchor: middle; fill: #e83e8c; }
        .main-flow-line { fill: none; stroke: #000000; stroke-width: 2; marker-end: url(#arrow-risk-enterprise); }
        .input-box { fill: #e9ecef; stroke: #495057; stroke-width: 1; rx: 5; ry: 5; }
    </style>

    <!-- 输入 -->
    <g id="input-data-enterprise">
        <rect x="20" y="270" width="160" height="60" class="input-box" filter="url(#shadow-enterprise)" />
        <text x="100" y="295" class="stage-title" font-size="16">云端实时信息</text>
        <text x="100" y="315" class="risk-item-text" font-size="12">(协议0x20)</text>
    </g>

    <!-- 1. 数据收集与入库 -->
    <g id="stage-collect-enterprise">
        <rect x="220" y="50" width="240" height="500" class="stage-box" filter="url(#shadow-enterprise)" />
        <text x="340" y="80" class="stage-title">1. 数据收集与入库</text>
        
        <rect x="240" y="110" width="200" height="80" class="risk-item-box" />
        <text x="340" y="135" class="risk-item-text">超资质范围收集</text>
        <text x="340" y="160" class="tech-text">技术: 备案资质比对</text>
        <text x="340" y="175" class="tech-text">协议字段: 数据用途</text>

        <rect x="240" y="210" width="200" height="80" class="risk-item-box" />
        <text x="340" y="235" class="risk-item-text">数据来源不合规</text>
        <text x="340" y="260" class="tech-text">技术: 来源方信息核查</text>
        <text x="340" y="275" class="tech-text">协议字段: 数据来源类型</text>
    </g>

    <!-- 2. 数据存储与处理 -->
    <g id="stage-store-process">
        <rect x="480" y="50" width="240" height="500" class="stage-box" filter="url(#shadow-enterprise)" />
        <text x="600" y="80" class="stage-title">2. 存储与处理</text>

        <rect x="500" y="110" width="200" height="80" class="risk-item-box" />
        <text x="600" y="135" class="risk-item-text">境外存储重要数据</text>
        <text x="600" y="160" class="tech-text">技术: IP地理位置识别</text>
        <text x="600" y="175" class="tech-text">协议字段: 存储区域标识</text>
        
        <rect x="500" y="210" width="200" height="80" class="risk-item-box" />
        <text x="600" y="235" class="risk-item-text">未脱敏加工敏感数据</text>
        <text x="600" y="260" class="tech-text">技术: 标志位检查</text>
        <text x="600" y="275" class="tech-text">协议字段: 脱敏状态</text>

        <rect x="500" y="310" width="200" height="80" class="risk-item-box" />
        <text x="600" y="335" class="risk-item-text">销毁流程违规</text>
        <text x="600" y="360" class="tech-text">技术: 状态检查</text>
        <text x="600" y="375" class="tech-text">协议字段: 审批状态</text>
    </g>

    <!-- 3. 数据共享与流转 -->
    <g id="stage-share-transfer">
        <rect x="740" y="50" width="240" height="500" class="stage-box" filter="url(#shadow-enterprise)" />
        <text x="860" y="80" class="stage-title">3. 共享与流转</text>

        <rect x="760" y="110" width="200" height="80" class="risk-item-box" />
        <text x="860" y="135" class="risk-item-text">违规跨境传输</text>
        <text x="860" y="160" class="tech-text">技术: 流量分析 &amp; IP识别</text>
        <text x="860" y="175" class="tech-text">协议字段: 传输目的地</text>

        <rect x="760" y="210" width="200" height="80" class="risk-item-box" />
        <text x="860" y="235" class="risk-item-text">向能力不足方提供</text>
        <text x="860" y="260" class="tech-text">技术: 接收方能力评估</text>
        <text x="860" y="275" class="tech-text">协议字段: 接收方安全能力</text>
        
        <rect x="760" y="310" width="200" height="80" class="risk-item-box" />
        <text x="860" y="335" class="risk-item-text">未经评估公开数据</text>
        <text x="860" y="360" class="tech-text">技术: 流程审计</text>
        <text x="860" y="375" class="tech-text">协议字段: 风险评估状态</text>
    </g>
    
    <!-- 4. 风险评估 -->
     <g id="stage-assess-enterprise">
        <rect x="1000" y="220" width="280" height="160" class="stage-box" filter="url(#shadow-enterprise)" />
        <text x="1140" y="250" class="stage-title">4. 综合风险评估</text>

        <text x="1140" y="285" class="risk-item-text">风险聚合 &amp; 信用评分</text>
        <text x="1140" y="305" class="risk-item-text">多因子加权评分 (AHP)</text>
        <text x="1140" y="325" class="risk-item-text">动态模型优化 (在线学习)</text>
        <text x="1140" y="355" class="stage-title" font-size="16" fill="#dc3545">-> 输出风险分值</text>
    </g>

    <!-- 连线 -->
    <path class="main-flow-line" d="M 180 300 H 220" />
    <path class="main-flow-line" d="M 460 300 H 480" />
    <path class="main-flow-line" d="M 720 300 H 740" />
    <path class="main-flow-line" d="M 980 300 H 1000" />
</svg>
