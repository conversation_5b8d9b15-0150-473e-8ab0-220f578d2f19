<svg width="950" height="400" xmlns="http://www.w3.org/2000/svg">
    <style>
        .rect-style {
            fill: #ffffff;
            stroke: #000000;
            stroke-width: 1;
            rx: 5; /* 圆角 */
            ry: 5;
        }
        .text-style {
            font-family: "Microsoft YaHei", "SimSun", sans-serif;
            font-size: 14px;
            font-weight: bold;
            text-anchor: middle;
            fill: #000000;
        }
        .line-style {
            fill: none;
            stroke: #000000;
            stroke-width: 1;
            marker-end: url(#arrow);
        }
        .sub-text-style {
            font-family: "Microsoft YaHei", "SimSun", sans-serif;
            font-size: 11px;
            text-anchor: middle;
            fill: #333333;
        }
        .group-text-style {
            font-family: "Microsoft YaHei", "SimSun", sans-serif;
            font-size: 12px;
            text-anchor: middle;
            fill: #555555;
        }
    </style>

    <defs>
        <marker id="arrow" viewBox="0 0 10 10" refX="5" refY="5"
            markerWidth="6" markerHeight="6"
            orient="auto-start-reverse">
          <path d="M 0 0 L 10 5 L 0 10 z" />
        </marker>
    </defs>

    <g id="data-source">
        <rect x="20" y="150" width="120" height="80" class="rect-style" />
        <text x="80" y="175" class="text-style">数据源</text>
        <text x="80" y="200" class="sub-text-style">车端/企业端</text>
        <text x="80" y="215" class="sub-text-style">实时二进制流</text>
    </g>

    <g id="data-ingestion">
        <rect x="180" y="150" width="120" height="80" class="rect-style" />
        <text x="240" y="175" class="text-style">数据接入</text>
        <text x="240" y="200" class="sub-text-style">协议解析 (Netty)</text>
        <text x="240" y="215" class="sub-text-style">身份认证</text>
    </g>
    
    <g id="message-queue">
        <rect x="340" y="150" width="120" height="80" class="rect-style" />
        <text x="400" y="175" class="text-style">消息队列</text>
        <text x="400" y="200" class="sub-text-style">数据缓冲 (Kafka)</text>
        <text x="400" y="215" class="sub-text-style">削峰填谷</text>
    </g>

    <g id="stream-processing">
        <rect x="500" y="150" width="120" height="80" class="rect-style" />
        <text x="560" y="175" class="text-style">流式处理</text>
        <text x="560" y="200" class="sub-text-style">数据预处理 (Flink)</text>
        <text x="560" y="215" class="sub-text-style">清洗/标准化</text>
    </g>

    <g id="risk-identification">
        <rect x="660" y="150" width="120" height="80" class="rect-style" />
        <text x="720" y="175" class="text-style">风险识别引擎</text>
        <text x="720" y="200" class="sub-text-style">规则匹配 (Drools)</text>
        <text x="720" y="215" class="sub-text-style">复杂事件处理(CEP)</text>
    </g>
    
    <g id="rule-base">
        <rect x="660" y="40" width="120" height="60" class="rect-style" />
        <text x="720" y="65" class="text-style">风险规则库</text>
        <text x="720" y="85" class="sub-text-style">车端/企业端规则</text>
    </g>
    
    <g id="risk-output">
        <rect x="820" y="150" width="120" height="80" class="rect-style" />
        <text x="880" y="175" class="text-style">风险事件输出</text>
        <text x="880" y="200" class="sub-text-style">分级预警</text>
        <text x="880" y="215" class="sub-text-style">联动处置</text>
    </g>

    <polyline points="140,190 180,190" class="line-style" />
    <polyline points="300,190 340,190" class="line-style" />
    <polyline points="460,190 500,190" class="line-style" />
    <polyline points="620,190 660,190" class="line-style" />
    <polyline points="720,100 720,150" class="line-style" />
    <polyline points="780,190 820,190" class="line-style" />

</svg>