<svg width="1300" height="450" xmlns="http://www.w3.org/2000/svg">
    <!-- SVG by Gemini -->
    <defs>
        <marker id="arrow-risk" viewBox="0 0 10 10" refX="5" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
          <path d="M 0 0 L 10 5 L 0 10 z" fill="#000000" />
        </marker>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
            <feOffset dx="2" dy="2" result="offsetblur"/>
            <feMerge>
                <feMergeNode/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>

    <style>
        .stage-box { fill: #f8f9fa; stroke: #6c757d; stroke-width: 1.5; rx: 8; ry: 8; }
        .stage-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; fill: #343a40; }
        .risk-item-box { fill: #ffffff; stroke: #ced4da; stroke-width: 1; rx: 4; ry: 4; }
        .risk-item-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 14px; text-anchor: middle; fill: #212529; }
        .tech-text { font-family: 'Courier New', monospace; font-size: 11px; text-anchor: middle; fill: #e83e8c; }
        .main-flow-line { fill: none; stroke: #000000; stroke-width: 2; marker-end: url(#arrow-risk); }
        .input-box { fill: #e9ecef; stroke: #495057; stroke-width: 1; rx: 5; ry: 5; }
    </style>

    <!-- 输入 -->
    <g id="input-data">
        <rect x="20" y="195" width="160" height="60" class="input-box" filter="url(#shadow)" />
        <text x="100" y="220" class="stage-title" font-size="16">车端实时信息</text>
        <text x="100" y="240" class="risk-item-text" font-size="12">(协议0x20)</text>
    </g>

    <!-- 1. 数据收集阶段 -->
    <g id="stage-collect">
        <rect x="220" y="50" width="240" height="350" class="stage-box" filter="url(#shadow)" />
        <text x="340" y="80" class="stage-title">1. 数据收集阶段</text>
        
        <rect x="240" y="110" width="200" height="80" class="risk-item-box" />
        <text x="340" y="135" class="risk-item-text">超出划定测试区域</text>
        <text x="340" y="160" class="tech-text">技术: 地理围栏(R-Tree)</text>
        <text x="340" y="175" class="tech-text">协议字段: 经度/纬度</text>

        <rect x="240" y="210" width="200" height="80" class="risk-item-box" />
        <text x="340" y="235" class="risk-item-text">收集频率异常</text>
        <text x="340" y="260" class="tech-text">技术: 时间序列分析</text>
        <text x="340" y="275" class="tech-text">逻辑: 滑动窗口统计</text>
    </g>

    <!-- 2. 数据存储阶段 -->
    <g id="stage-store">
        <rect x="480" y="50" width="240" height="350" class="stage-box" filter="url(#shadow)" />
        <text x="600" y="80" class="stage-title">2. 数据存储阶段</text>

        <rect x="500" y="110" width="200" height="80" class="risk-item-box" />
        <text x="600" y="135" class="risk-item-text">明文存储敏感数据</text>
        <text x="600" y="160" class="tech-text">技术: 状态解析</text>
        <text x="600" y="175" class="tech-text">协议字段: 加密存储状态</text>
        
        <rect x="500" y="210" width="200" height="80" class="risk-item-box" />
        <text x="600" y="235" class="risk-item-text">存储里程超限</text>
        <text x="600" y="260" class="tech-text">技术: 阈值监控</text>
        <text x="600" y="275" class="tech-text">协议字段: 本次存储数据关联里程</text>
    </g>

    <!-- 3. 数据传输阶段 -->
    <g id="stage-transfer">
        <rect x="740" y="50" width="240" height="350" class="stage-box" filter="url(#shadow)" />
        <text x="860" y="80" class="stage-title">3. 数据传输阶段</text>

        <rect x="760" y="110" width="200" height="80" class="risk-item-box" />
        <text x="860" y="135" class="risk-item-text">传输未合规处理坐标</text>
        <text x="860" y="160" class="tech-text">技术: 标志位检查</text>
        <text x="860" y="175" class="tech-text">协议字段: 坐标处理标志</text>

        <rect x="760" y="210" width="200" height="80" class="risk-item-box" />
        <text x="860" y="235" class="risk-item-text">向未授权目的地传输</text>
        <text x="860" y="260" class="tech-text">技术: 备案信息比对</text>
        <text x="860" y="275" class="tech-text">协议字段: 传输目的地</text>
    </g>
    
    <!-- 4. 风险评估 -->
     <g id="stage-assess">
        <rect x="1000" y="145" width="280" height="160" class="stage-box" filter="url(#shadow)" />
        <text x="1140" y="175" class="stage-title">4. 综合风险评估</text>

        <text x="1140" y="210" class="risk-item-text">多因子加权评分 (AHP)</text>
        <text x="1140" y="230" class="risk-item-text">模糊综合评价</text>
        <text x="1140" y="250" class="risk-item-text">动态模型优化 (在线学习)</text>
        <text x="1140" y="280" class="stage-title" font-size="16" fill="#dc3545">-> 输出风险分值</text>
    </g>

    <!-- 连线 -->
    <path class="main-flow-line" d="M 180 225 H 220" />
    <path class="main-flow-line" d="M 460 225 H 480" />
    <path class="main-flow-line" d="M 720 225 H 740" />
    <path class="main-flow-line" d="M 980 225 H 1000" />
</svg>
