<svg viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Arial', sans-serif; font-size: 20px; font-weight: bold; fill: #1f2937; }
      .subtitle { font-family: 'Arial', sans-serif; font-size: 14px; fill: #4b5563; }
      .swimlane-title { font-family: 'Arial', sans-serif; font-size: 16px; font-weight: bold; fill: #374151; }
      .process-box { fill: #dbeafe; stroke: #3b82f6; stroke-width: 2; }
      .decision-box { fill: #fef3c7; stroke: #f59e0b; stroke-width: 2; }
      .document-box { fill: #ecfdf5; stroke: #10b981; stroke-width: 2; }
      .start-end-box { fill: #fee2e2; stroke: #ef4444; stroke-width: 2; }
      .process-text { font-family: 'Arial', sans-serif; font-size: 12px; fill: #1f2937; text-anchor: middle; }
      .arrow { stroke: #374151; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .swimlane-bg1 { fill: #f8fafc; }
      .swimlane-bg2 { fill: #f1f5f9; }
      .phase-separator { stroke: #d1d5db; stroke-width: 1; stroke-dasharray: 5,5; }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="600" y="25" class="title" text-anchor="middle">时空数据监测平台企业授权认证业务流程</text>
  <text x="600" y="45" class="subtitle" text-anchor="middle">Enterprise Authorization and Authentication Process Flow</text>
  
  <!-- Swimlanes Background -->
  <rect x="0" y="70" width="1200" height="160" class="swimlane-bg1" />
  <rect x="0" y="230" width="1200" height="160" class="swimlane-bg2" />
  <rect x="0" y="390" width="1200" height="160" class="swimlane-bg1" />
  <rect x="0" y="550" width="1200" height="180" class="swimlane-bg2" />
  
  <!-- Swimlane Titles -->
  <text x="20" y="90" class="swimlane-title">企业方</text>
  <text x="20" y="110" class="subtitle">Enterprise</text>
  
  <text x="20" y="250" class="swimlane-title">平台管理员</text>
  <text x="20" y="270" class="subtitle">Platform Admin</text>
  
  <text x="20" y="410" class="swimlane-title">系统认证</text>
  <text x="20" y="430" class="subtitle">System Auth</text>
  
  <text x="20" y="570" class="swimlane-title">备案审核</text>
  <text x="20" y="590" class="subtitle">Filing Review</text>
  
  <!-- Phase Separators -->
  <line x1="300" y1="70" x2="300" y2="730" class="phase-separator" />
  <line x1="550" y1="70" x2="550" y2="730" class="phase-separator" />
  <line x1="800" y1="70" x2="800" y2="730" class="phase-separator" />
  
  <!-- Phase Labels -->
  <text x="175" y="90" class="subtitle" text-anchor="middle" fill="#6b7280">1. 初始联系阶段</text>
  <text x="425" y="90" class="subtitle" text-anchor="middle" fill="#6b7280">2. 授权文件处理</text>
  <text x="675" y="90" class="subtitle" text-anchor="middle" fill="#6b7280">3. 账号创建认证</text>
  <text x="1000" y="90" class="subtitle" text-anchor="middle" fill="#6b7280">4. 企业备案完成</text>
  
  <!-- Process Boxes -->
  
  <!-- Step 1: Enterprise Contact -->
  <ellipse cx="175" cy="150" rx="60" ry="25" class="start-end-box" />
  <text x="175" y="145" class="process-text">开始</text>
  <text x="175" y="158" class="process-text">企业联系</text>
  
  <!-- Step 2: Prepare Authorization -->
  <rect x="120" y="180" width="110" height="40" rx="5" class="process-box" />
  <text x="175" y="195" class="process-text">准备授权材料</text>
  <text x="175" y="208" class="process-text">联系人+身份信息</text>
  
  <!-- Step 3: Document Processing -->
  <rect x="350" y="120" width="110" height="40" rx="5" class="document-box" />
  <text x="405" y="135" class="process-text">盖章打印</text>
  <text x="405" y="148" class="process-text">授权文件</text>
  
  <!-- Step 4: Submit to Platform -->
  <rect x="350" y="180" width="110" height="40" rx="5" class="process-box" />
  <text x="405" y="195" class="process-text">提交文件给</text>
  <text x="405" y="208" class="process-text">平台管理员</text>
  
  <!-- Step 5: Admin Receives -->
  <rect x="350" y="250" width="110" height="40" rx="5" class="process-box" />
  <text x="405" y="265" class="process-text">接收授权文件</text>
  <text x="405" y="278" class="process-text">审核材料</text>
  
  <!-- Step 6: Document Review Decision -->
  <polygon points="480,310 520,330 480,350 440,330" class="decision-box" />
  <text x="480" y="325" class="process-text">文件</text>
  <text x="480" y="338" class="process-text">合规？</text>
  
  <!-- Step 7: Create Admin Account -->
  <rect x="600" y="250" width="110" height="40" rx="5" class="process-box" />
  <text x="655" y="265" class="process-text">创建管理员</text>
  <text x="655" y="278" class="process-text">账号</text>
  
  <!-- Step 8: SMS Verification -->
  <rect x="600" y="410" width="110" height="40" rx="5" class="process-box" />
  <text x="655" y="425" class="process-text">手机号短信</text>
  <text x="655" y="438" class="process-text">实名认证</text>
  
  <!-- Step 9: Account Auth Decision -->
  <polygon points="730,430 770,450 730,470 690,450" class="decision-box" />
  <text x="730" y="445" class="process-text">账号</text>
  <text x="730" y="458" class="process-text">认证？</text>
  
  <!-- Step 10: Enterprise Real-name Auth -->
  <rect x="850" y="120" width="110" height="40" rx="5" class="process-box" />
  <text x="905" y="135" class="process-text">企业实名</text>
  <text x="905" y="148" class="process-text">认证</text>
  
  <!-- Step 11: Enterprise Auth Decision -->
  <polygon points="905,190 945,210 905,230 865,210" class="decision-box" />
  <text x="905" y="205" class="process-text">企业</text>
  <text x="905" y="218" class="process-text">认证？</text>
  
  <!-- Step 12: Filing Application -->
  <rect x="850" y="570" width="110" height="40" rx="5" class="process-box" />
  <text x="905" y="585" class="process-text">提交备案</text>
  <text x="905" y="598" class="process-text">申请</text>
  
  <!-- Step 13: Filing Review -->
  <rect x="850" y="630" width="110" height="40" rx="5" class="process-box" />
  <text x="905" y="645" class="process-text">备案审核</text>
  <text x="905" y="658" class="process-text">与批准</text>
  
  <!-- Step 14: Process Complete -->
  <ellipse cx="1050" cy="650" rx="60" ry="25" class="start-end-box" />
  <text x="1050" y="645" class="process-text">流程完成</text>
  <text x="1050" y="658" class="process-text">企业备案成功</text>
  
  <!-- Arrows -->
  <!-- 1 to 2 -->
  <line x1="175" y1="175" x2="175" y2="180" class="arrow" />
  
  <!-- 2 to 3 -->
  <line x1="230" y1="200" x2="350" y2="140" class="arrow" />
  
  <!-- 3 to 4 -->
  <line x1="405" y1="160" x2="405" y2="180" class="arrow" />
  
  <!-- 4 to 5 -->
  <line x1="405" y1="220" x2="405" y2="250" class="arrow" />
  
  <!-- 5 to 6 -->
  <line x1="405" y1="290" x2="440" y2="330" class="arrow" />
  
  <!-- 6 to 7 (Yes) -->
  <line x1="520" y1="330" x2="600" y2="270" class="arrow" />
  
  <!-- 6 to 2 (No) -->
  <path d="M 440,330 Q 340,360 240,200" fill="none" stroke="#ef4444" stroke-width="2" marker-end="url(#arrowhead)" />
  
  <!-- 7 to 8 -->
  <line x1="655" y1="290" x2="655" y2="410" class="arrow" />
  
  <!-- 8 to 9 -->
  <line x1="710" y1="430" x2="690" y2="450" class="arrow" />
  
  <!-- 9 to 10 (Yes) -->
  <line x1="770" y1="450" x2="850" y2="140" class="arrow" />
  
  <!-- 9 to 8 (No) -->
  <path d="M 690,450 Q 620,480 655,450" fill="none" stroke="#ef4444" stroke-width="2" marker-end="url(#arrowhead)" />
  
  <!-- 10 to 11 -->
  <line x1="905" y1="160" x2="905" y2="190" class="arrow" />
  
  <!-- 11 to 12 (Yes) -->
  <line x1="905" y1="230" x2="905" y2="570" class="arrow" />
  
  <!-- 11 to 10 (No) -->
  <path d="M 865,210 Q 825,190 850,140" fill="none" stroke="#ef4444" stroke-width="2" marker-end="url(#arrowhead)" />
  
  <!-- 12 to 13 -->
  <line x1="905" y1="610" x2="905" y2="630" class="arrow" />
  
  <!-- 13 to 14 -->
  <line x1="960" y1="650" x2="990" y2="650" class="arrow" />
  
  <!-- Decision Labels -->
  <text x="540" y="320" class="subtitle" fill="#10b981">是</text>
  <text x="370" y="350" class="subtitle" fill="#ef4444">否，重新准备</text>
  
  <text x="780" y="440" class="subtitle" fill="#10b981">是</text>
  <text x="650" y="480" class="subtitle" fill="#ef4444">否，重新验证</text>
  
  <text x="920" y="250" class="subtitle" fill="#10b981">是</text>
  <text x="820" y="180" class="subtitle" fill="#ef4444">否，重新认证</text>
  
  <!-- Legend -->
  <rect x="50" y="750" width="1100" height="40" fill="#f9fafb" stroke="#e5e7eb" stroke-width="1" rx="5" />
  
  <rect x="70" y="760" width="15" height="10" class="process-box" />
  <text x="95" y="768" class="subtitle">处理步骤</text>
  
  <polygon points="150,760 160,765 150,770 140,765" class="decision-box" />
  <text x="170" y="768" class="subtitle">决策点</text>
  
  <rect x="240" y="760" width="15" height="10" class="document-box" />
  <text x="265" y="768" class="subtitle">文档处理</text>
  
  <ellipse cx="340" cy="765" rx="12" ry="5" class="start-end-box" />
  <text x="360" y="768" class="subtitle">开始/结束</text>
  
  <line x1="420" y1="765" x2="450" y2="765" class="arrow" />
  <text x="460" y="768" class="subtitle">流程方向</text>
  
  <line x1="520" y1="765" x2="550" y2="765" stroke="#ef4444" stroke-width="2" marker-end="url(#arrowhead)" />
  <text x="560" y="768" class="subtitle" fill="#ef4444">返回重做</text>
</svg>