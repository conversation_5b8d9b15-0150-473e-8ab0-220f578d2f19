<svg width="1200" height="750" xmlns="http://www.w3.org/2000/svg" font-family="sans-serif">
    <!-- 定义样式 -->
    <style>
        .title { font-size: 20px; font-weight: bold; text-anchor: middle; }
        .subtitle { font-size: 16px; font-weight: bold; text-anchor: middle; }
        .box { fill: #ffffff; stroke: #333; stroke-width: 1.5; rx: 8; ry: 8; }
        .box-title { font-size: 14px; font-weight: bold; text-anchor: middle; }
        .box-text { font-size: 12px; text-anchor: middle; }
        .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        .dashed-arrow { stroke: #666; stroke-width: 1.5; fill: none; stroke-dasharray: 5,5; marker-end: url(#arrowhead); }
        .flow-label { font-size: 12px; fill: #555; font-weight: bold; }
        .icon { font-size: 24px; }
        .connection-line { stroke: #4a90e2; stroke-width: 2; fill: none; }
        .feedback-line { stroke: #e74c3c; stroke-width: 1.5; fill: none; stroke-dasharray: 3,3; }
    </style>

    <!-- 定义箭头 -->
    <defs>
        <marker id="arrowhead" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
            <path d="M 0 0 L 10 5 L 0 10 z" fill="#333" />
        </marker>
        <marker id="blue-arrowhead" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
            <path d="M 0 0 L 10 5 L 0 10 z" fill="#4a90e2" />
        </marker>
        <marker id="red-arrowhead" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
            <path d="M 0 0 L 10 5 L 0 10 z" fill="#e74c3c" />
        </marker>
    </defs>

    <!-- 标题 -->
    <text x="600" y="40" class="title">智能网联汽车时空数据通用风险识别模型综合架构</text>

    <!-- 左侧: 实时信息输入 -->
    <rect x="20" y="80" width="260" height="580" class="box" fill="#f8f9fa"/>
    <text x="150" y="110" class="subtitle">实时信息输入</text>

    <!-- 数据源矩阵 -->
    <g>
        <text x="150" y="160" class="box-title">数据源</text>

        <!-- 车端数据矩阵 -->
        <rect x="40" y="170" width="100" height="35" class="box" fill="#e3f2fd"/>
        <text x="60" y="185" class="icon" font-size="16px">🚗</text>
        <text x="90" y="190" class="box-text" font-size="11px">车端实时流</text>

        <rect x="40" y="210" width="48" height="25" class="box" fill="#f3e5f5"/>
        <text x="64" y="225" class="box-text" font-size="10px">轨迹</text>

        <rect x="92" y="210" width="48" height="25" class="box" fill="#f3e5f5"/>
        <text x="116" y="225" class="box-text" font-size="10px">影像</text>

        <rect x="144" y="210" width="48" height="25" class="box" fill="#f3e5f5"/>
        <text x="168" y="225" class="box-text" font-size="10px">点云</text>

        <rect x="196" y="210" width="48" height="25" class="box" fill="#f3e5f5"/>
        <text x="220" y="225" class="box-text" font-size="10px">惯导</text>

        <!-- 云端数据矩阵 -->
        <rect x="160" y="170" width="100" height="35" class="box" fill="#fff3e0"/>
        <text x="180" y="185" class="icon" font-size="16px">☁️</text>
        <text x="210" y="190" class="box-text" font-size="11px">云端业务流</text>
    </g>

    <!-- 数据接入与预处理矩阵 -->
    <g>
        <text x="150" y="300" class="box-title">数据接入与预处理</text>

        <!-- 第一行处理模块 -->
        <rect x="40" y="310" width="100" height="30" class="box" fill="#e8f5e8"/>
        <text x="90" y="330" class="box-text" font-size="11px">协议解析(Netty)</text>

        <rect x="150" y="310" width="110" height="30" class="box" fill="#fff8e1"/>
        <text x="205" y="330" class="box-text" font-size="11px">身份认证与鉴权</text>

        <!-- 第二行处理模块 -->
        <rect x="40" y="350" width="100" height="30" class="box" fill="#f3e5f5"/>
        <text x="90" y="370" class="box-text" font-size="11px">消息队列(Kafka)</text>

        <rect x="150" y="350" width="110" height="30" class="box" fill="#e1f5fe"/>
        <text x="205" y="370" class="box-text" font-size="11px">数据清洗(Flink)</text>

        <!-- 第三行细节模块 -->
        <rect x="40" y="390" width="70" height="25" class="box" fill="#fce4ec"/>
        <text x="75" y="405" class="box-text" font-size="10px">格式转换</text>

        <rect x="115" y="390" width="70" height="25" class="box" fill="#fce4ec"/>
        <text x="150" y="405" class="box-text" font-size="10px">质量校验</text>

        <rect x="190" y="390" width="70" height="25" class="box" fill="#fce4ec"/>
        <text x="225" y="405" class="box-text" font-size="10px">窗口聚合</text>
    </g>

    <!-- 优化的垂直连线 -->
    <path d="M 150,240 L 150,300" class="connection-line" marker-end="url(#blue-arrowhead)" />

    <!-- 标准化事件流矩阵 -->
    <g>
        <rect x="40" y="450" width="220" height="80" class="box" fill="#f0f8ff"/>
        <text x="150" y="470" class="box-title">标准化事件流</text>

        <rect x="50" y="480" width="90" height="25" class="box" fill="#e3f2fd"/>
        <text x="95" y="495" class="box-text" font-size="10px">结构化数据</text>

        <rect x="150" y="480" width="90" height="25" class="box" fill="#e8f5e8"/>
        <text x="195" y="495" class="box-text" font-size="10px">规范化格式</text>

        <rect x="100" y="510" width="100" height="20" class="box" fill="#fff3e0"/>
        <text x="150" y="522" class="box-text" font-size="10px">待分析事件流</text>
    </g>

    <!-- 优化的垂直连线 -->
    <path d="M 150,420 L 150,450" class="connection-line" marker-end="url(#blue-arrowhead)" />

    <!-- 中间: 模型技术矩阵 -->
    <rect x="300" y="80" width="580" height="580" class="box" fill="#f8f9fa"/>
    <text x="590" y="110" class="subtitle">模型技术矩阵与风险识别引擎</text>

    <!-- 优化的主要输入连线 -->
    <path d="M 260,530 L 300,530" class="connection-line" marker-end="url(#blue-arrowhead)" />
    <text x="270" y="520" class="flow-label">数据输入</text>

    <!-- 风险识别模型层 -->
    <g>
        <rect x="320" y="140" width="540" height="500" class="box"/>
        <text x="590" y="160" class="box-title">多维风险识别模型</text>

        <!-- 规则驱动模型矩阵 -->
        <g>
            <rect x="340" y="180" width="240" height="140" class="box" fill="#f8f9fa"/>
            <text x="460" y="200" class="box-title">1. 规则驱动模型</text>
            <text x="360" y="220" class="icon" font-size="20px">📜</text>

            <!-- 技术矩阵 -->
            <rect x="380" y="210" width="80" height="25" class="box" fill="#e3f2fd"/>
            <text x="420" y="225" class="box-text" font-size="10px">Drools引擎</text>

            <rect x="470" y="210" width="80" height="25" class="box" fill="#e8f5e8"/>
            <text x="510" y="225" class="box-text" font-size="10px">RETE算法</text>

            <rect x="380" y="240" width="80" height="25" class="box" fill="#fff3e0"/>
            <text x="420" y="255" class="box-text" font-size="10px">动态规则库</text>

            <rect x="470" y="240" width="80" height="25" class="box" fill="#fce4ec"/>
            <text x="510" y="255" class="box-text" font-size="10px">热部署</text>

            <!-- 应用场景 -->
            <rect x="350" y="280" width="180" height="30" class="box" fill="#f3e5f5"/>
            <text x="440" y="300" class="box-text" font-size="11px">明确违规行为识别</text>
        </g>

        <!-- 时空行为分析模型矩阵 -->
        <g>
            <rect x="600" y="180" width="240" height="140" class="box" fill="#f8f9fa"/>
            <text x="720" y="200" class="box-title">2. 时空行为分析</text>
            <text x="620" y="220" class="icon" font-size="20px">🗺️</text>

            <!-- 技术矩阵 -->
            <rect x="640" y="210" width="80" height="25" class="box" fill="#e1f5fe"/>
            <text x="680" y="225" class="box-text" font-size="10px">地理围栏</text>

            <rect x="730" y="210" width="80" height="25" class="box" fill="#e8f5e8"/>
            <text x="770" y="225" class="box-text" font-size="10px">轨迹聚类</text>

            <rect x="640" y="240" width="80" height="25" class="box" fill="#fff8e1"/>
            <text x="680" y="255" class="box-text" font-size="10px">异常检测</text>

            <rect x="730" y="240" width="80" height="25" class="box" fill="#fce4ec"/>
            <text x="770" y="255" class="box-text" font-size="10px">精度分析</text>

            <!-- 应用场景 -->
            <rect x="610" y="280" width="180" height="30" class="box" fill="#f3e5f5"/>
            <text x="700" y="300" class="box-text" font-size="11px">空间违规·异常聚集</text>
        </g>

        <!-- 机器学习模型矩阵 -->
        <g>
            <rect x="340" y="340" width="240" height="140" class="box" fill="#f8f9fa"/>
            <text x="460" y="360" class="box-title">3. 机器学习模型</text>
            <text x="360" y="380" class="icon" font-size="20px">🤖</text>

            <!-- 技术矩阵 -->
            <rect x="380" y="370" width="80" height="25" class="box" fill="#e3f2fd"/>
            <text x="420" y="385" class="box-text" font-size="10px">基线建模</text>

            <rect x="470" y="370" width="80" height="25" class="box" fill="#e8f5e8"/>
            <text x="510" y="385" class="box-text" font-size="10px">异常检测</text>

            <rect x="380" y="400" width="80" height="25" class="box" fill="#fff3e0"/>
            <text x="420" y="415" class="box-text" font-size="10px">Forest算法</text>

            <rect x="470" y="400" width="80" height="25" class="box" fill="#fce4ec"/>
            <text x="510" y="415" class="box-text" font-size="10px">风险评分</text>

            <!-- 应用场景 -->
            <rect x="350" y="440" width="180" height="30" class="box" fill="#f3e5f5"/>
            <text x="440" y="460" class="box-text" font-size="11px">未知风险·综合评估</text>
        </g>

        <!-- 复杂事件处理矩阵 -->
        <g>
            <rect x="600" y="340" width="240" height="140" class="box" fill="#f8f9fa"/>
            <text x="720" y="360" class="box-title">4. 复杂事件处理</text>
            <text x="620" y="380" class="icon" font-size="20px">⚡</text>

            <!-- 技术矩阵 -->
            <rect x="640" y="370" width="80" height="25" class="box" fill="#e1f5fe"/>
            <text x="680" y="385" class="box-text" font-size="10px">Flink CEP</text>

            <rect x="730" y="370" width="80" height="25" class="box" fill="#e8f5e8"/>
            <text x="770" y="385" class="box-text" font-size="10px">模式匹配</text>

            <rect x="640" y="400" width="80" height="25" class="box" fill="#fff8e1"/>
            <text x="680" y="415" class="box-text" font-size="10px">事件聚合</text>

            <rect x="730" y="400" width="80" height="25" class="box" fill="#fce4ec"/>
            <text x="770" y="415" class="box-text" font-size="10px">关联分析</text>

            <!-- 应用场景 -->
            <rect x="610" y="440" width="180" height="30" class="box" fill="#f3e5f5"/>
            <text x="700" y="460" class="box-text" font-size="11px">复合型风险识别</text>
        </g>
    </g>

    <!-- 风险识别引擎核心矩阵 -->
    <g>
        <rect x="320" y="530" width="540" height="100" class="box" fill="#e9ecef"/>
        <text x="590" y="550" class="box-title">风险识别引擎核心</text>

        <!-- 处理流程矩阵 -->
        <rect x="340" y="560" width="120" height="30" class="box" fill="#e3f2fd"/>
        <text x="400" y="580" class="box-text" font-size="11px">模型协同决策</text>

        <path d="M 470,575 L 490,575" stroke="#4a90e2" stroke-width="2" marker-end="url(#blue-arrowhead)"/>

        <rect x="500" y="560" width="120" height="30" class="box" fill="#e8f5e8"/>
        <text x="560" y="580" class="box-text" font-size="11px">风险判定聚合</text>

        <path d="M 630,575 L 650,575" stroke="#4a90e2" stroke-width="2" marker-end="url(#blue-arrowhead)"/>

        <rect x="660" y="560" width="120" height="30" class="box" fill="#fff3e0"/>
        <text x="720" y="580" class="box-text" font-size="11px">风险事件生成</text>

        <!-- 子功能矩阵 -->
        <rect x="340" y="595" width="80" height="20" class="box" fill="#fce4ec"/>
        <text x="380" y="607" class="box-text" font-size="9px">权重计算</text>

        <rect x="430" y="595" width="80" height="20" class="box" fill="#fce4ec"/>
        <text x="470" y="607" class="box-text" font-size="9px">阈值判定</text>

        <rect x="520" y="595" width="80" height="20" class="box" fill="#fce4ec"/>
        <text x="560" y="607" class="box-text" font-size="9px">级别分类</text>

        <rect x="610" y="595" width="80" height="20" class="box" fill="#fce4ec"/>
        <text x="650" y="607" class="box-text" font-size="9px">事件封装</text>

        <rect x="700" y="595" width="80" height="20" class="box" fill="#fce4ec"/>
        <text x="740" y="607" class="box-text" font-size="9px">输出格式</text>
    </g>

    <!-- 优化的模型间连线 - 避免遮挡，连接到矩阵中心 -->
    <!-- 从输入到规则驱动模型 -->
    <path d="M 320,490 L 300,490 L 300,250 L 340,250" class="dashed-arrow" />

    <!-- 从输入到时空行为分析模型 -->
    <path d="M 320,490 L 300,490 L 300,200 L 580,200 L 580,250 L 600,250" class="dashed-arrow" />

    <!-- 从输入到机器学习模型 -->
    <path d="M 320,490 L 300,490 L 300,410 L 340,410" class="dashed-arrow" />

    <!-- 从输入到复杂事件处理 -->
    <path d="M 320,490 L 300,490 L 300,360 L 580,360 L 580,410 L 600,410" class="dashed-arrow" />

    <!-- 从模型到引擎核心的连线 - 连接到矩阵底部 -->
    <path d="M 460,470 L 460,560" class="connection-line" marker-end="url(#blue-arrowhead)" />
    <path d="M 720,470 L 720,560" class="connection-line" marker-end="url(#blue-arrowhead)" />
    
    <!-- 右侧: 风险事件输出 -->
    <rect x="900" y="80" width="280" height="580" class="box" fill="#f8f9fa"/>
    <text x="1040" y="110" class="subtitle">风险事件输出</text>

    <!-- 优化的主要输出连线 -->
    <path d="M 860,575 L 920,575 L 920,220 L 930,220" class="connection-line" marker-end="url(#blue-arrowhead)" />
    <text x="870" y="565" class="flow-label">风险输出</text>

    <!-- 风险事件分级矩阵 -->
    <g>
        <text x="1040" y="160" class="box-title">1. 风险事件分级</text>

        <!-- 风险等级矩阵 -->
        <rect x="930" y="170" width="80" height="25" class="box" fill="#ffebee"/>
        <text x="970" y="185" class="box-text" font-size="10px" fill="red">特别重大</text>

        <rect x="1020" y="170" width="60" height="25" class="box" fill="#fff3e0"/>
        <text x="1050" y="185" class="box-text" font-size="10px" fill="orange">重大</text>

        <rect x="1090" y="170" width="60" height="25" class="box" fill="#fef7e0"/>
        <text x="1120" y="185" class="box-text" font-size="10px" fill="#e67e22">较大</text>

        <rect x="930" y="200" width="60" height="25" class="box" fill="#fffde7"/>
        <text x="960" y="215" class="box-text" font-size="10px" fill="#f1c40f">一般</text>

        <rect x="1000" y="200" width="60" height="25" class="box" fill="#e8f5e8"/>
        <text x="1030" y="215" class="box-text" font-size="10px" fill="#2ecc71">轻微</text>

        <!-- 分级标准矩阵 -->
        <rect x="930" y="235" width="45" height="20" class="box" fill="#f3e5f5"/>
        <text x="952" y="247" class="box-text" font-size="9px">L1-L5</text>

        <rect x="980" y="235" width="45" height="20" class="box" fill="#f3e5f5"/>
        <text x="1002" y="247" class="box-text" font-size="9px">评分</text>

        <rect x="1030" y="235" width="45" height="20" class="box" fill="#f3e5f5"/>
        <text x="1052" y="247" class="box-text" font-size="9px">阈值</text>

        <rect x="1080" y="235" width="45" height="20" class="box" fill="#f3e5f5"/>
        <text x="1102" y="247" class="box-text" font-size="9px">权重</text>

        <rect x="1130" y="235" width="45" height="20" class="box" fill="#f3e5f5"/>
        <text x="1152" y="247" class="box-text" font-size="9px">优先级</text>
    </g>

    <!-- 优化的垂直连线 -->
    <path d="M 1040,260 L 1040,340" class="connection-line" marker-end="url(#blue-arrowhead)" />

    <!-- 监测预警与处置联动矩阵 -->
    <g>
        <text x="1040" y="340" class="box-title">2. 监测预警与处置联动</text>

        <!-- 预警通道矩阵 -->
        <text x="940" y="360" class="icon" font-size="18px">🔔</text>
        <rect x="960" y="350" width="50" height="25" class="box" fill="#e3f2fd"/>
        <text x="985" y="365" class="box-text" font-size="10px">短信</text>

        <rect x="1020" y="350" width="50" height="25" class="box" fill="#e8f5e8"/>
        <text x="1045" y="365" class="box-text" font-size="10px">邮件</text>

        <rect x="1080" y="350" width="50" height="25" class="box" fill="#fff3e0"/>
        <text x="1105" y="365" class="box-text" font-size="10px">APP</text>

        <rect x="1140" y="350" width="50" height="25" class="box" fill="#fce4ec"/>
        <text x="1165" y="365" class="box-text" font-size="10px">语音</text>

        <!-- 预警机制矩阵 -->
        <rect x="930" y="385" width="70" height="25" class="box" fill="#f3e5f5"/>
        <text x="965" y="400" class="box-text" font-size="10px">升级机制</text>

        <rect x="1010" y="385" width="70" height="25" class="box" fill="#f3e5f5"/>
        <text x="1045" y="400" class="box-text" font-size="10px">频率控制</text>

        <rect x="1090" y="385" width="70" height="25" class="box" fill="#f3e5f5"/>
        <text x="1125" y="400" class="box-text" font-size="10px">优先级</text>

        <!-- 处置联动矩阵 -->
        <text x="940" y="430" class="icon" font-size="18px">⚙️</text>
        <rect x="960" y="420" width="70" height="25" class="box" fill="#e1f5fe"/>
        <text x="995" y="435" class="box-text" font-size="10px">API联动</text>

        <rect x="1040" y="420" width="70" height="25" class="box" fill="#e8f5e8"/>
        <text x="1075" y="435" class="box-text" font-size="10px">自动处置</text>

        <rect x="1120" y="420" width="70" height="25" class="box" fill="#fff8e1"/>
        <text x="1155" y="435" class="box-text" font-size="10px">工单系统</text>

        <!-- 协同处理矩阵 -->
        <rect x="930" y="455" width="60" height="20" class="box" fill="#fce4ec"/>
        <text x="960" y="467" class="box-text" font-size="9px">人工审核</text>

        <rect x="1000" y="455" width="60" height="20" class="box" fill="#fce4ec"/>
        <text x="1030" y="467" class="box-text" font-size="9px">流程跟踪</text>

        <rect x="1070" y="455" width="60" height="20" class="box" fill="#fce4ec"/>
        <text x="1100" y="467" class="box-text" font-size="9px">结果反馈</text>
    </g>

    <!-- 优化的垂直连线 -->
    <path d="M 1040,480 L 1040,520" class="connection-line" marker-end="url(#blue-arrowhead)" />

    <!-- 可视化与溯源分析矩阵 -->
    <g>
        <text x="1040" y="520" class="box-title">3. 可视化与溯源分析</text>

        <!-- 可视化矩阵 -->
        <text x="940" y="540" class="icon" font-size="18px">📊</text>
        <rect x="960" y="530" width="80" height="25" class="box" fill="#e3f2fd"/>
        <text x="1000" y="545" class="box-text" font-size="10px">态势大屏</text>

        <rect x="1050" y="530" width="80" height="25" class="box" fill="#e8f5e8"/>
        <text x="1090" y="545" class="box-text" font-size="10px">实时监控</text>

        <rect x="960" y="560" width="80" height="25" class="box" fill="#fff3e0"/>
        <text x="1000" y="575" class="box-text" font-size="10px">合规报表</text>

        <rect x="1050" y="560" width="80" height="25" class="box" fill="#fce4ec"/>
        <text x="1090" y="575" class="box-text" font-size="10px">统计分析</text>

        <!-- 溯源分析矩阵 -->
        <text x="940" y="600" class="icon" font-size="18px">🔍</text>
        <rect x="960" y="590" width="80" height="25" class="box" fill="#e1f5fe"/>
        <text x="1000" y="605" class="box-text" font-size="10px">血缘追踪</text>

        <rect x="1050" y="590" width="80" height="25" class="box" fill="#e8f5e8"/>
        <text x="1090" y="605" class="box-text" font-size="10px">事件回溯</text>

        <rect x="960" y="620" width="80" height="25" class="box" fill="#fff8e1"/>
        <text x="1000" y="635" class="box-text" font-size="10px">责任认定</text>

        <rect x="1050" y="620" width="80" height="25" class="box" fill="#f3e5f5"/>
        <text x="1090" y="635" class="box-text" font-size="10px">审计日志</text>

        <!-- 分析工具矩阵 -->
        <rect x="930" y="655" width="50" height="20" class="box" fill="#fce4ec"/>
        <text x="955" y="667" class="box-text" font-size="9px">时序图</text>

        <rect x="990" y="655" width="50" height="20" class="box" fill="#fce4ec"/>
        <text x="1015" y="667" class="box-text" font-size="9px">关系图</text>

        <rect x="1050" y="655" width="50" height="20" class="box" fill="#fce4ec"/>
        <text x="1075" y="667" class="box-text" font-size="9px">热力图</text>

        <rect x="1110" y="655" width="50" height="20" class="box" fill="#fce4ec"/>
        <text x="1135" y="667" class="box-text" font-size="9px">流向图</text>
    </g>

    <!-- 添加反馈连线 -->
    <path d="M 920,400 L 880,400 L 880,720 L 300,720 L 300,620 L 320,620"
          class="feedback-line" marker-end="url(#red-arrowhead)" />
    <text x="600" y="735" class="flow-label" fill="#e74c3c">反馈优化与模型调整</text>

</svg>
