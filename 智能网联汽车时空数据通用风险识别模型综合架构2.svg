<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg" font-family="sans-serif">
    <!-- 定义样式 -->
    <style>
        .title { font-size: 20px; font-weight: bold; text-anchor: middle; }
        .subtitle { font-size: 16px; font-weight: bold; text-anchor: middle; }
        .box { fill: #ffffff; stroke: #333; stroke-width: 1.5; rx: 8; ry: 8; }
        .box-title { font-size: 14px; font-weight: bold; text-anchor: middle; }
        .box-text { font-size: 12px; text-anchor: middle; }
        .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
        .dashed-arrow { stroke: #666; stroke-width: 1.5; fill: none; stroke-dasharray: 5,5; marker-end: url(#arrowhead); }
        .flow-label { font-size: 12px; fill: #555; font-weight: bold; }
        .icon { font-size: 24px; }
        .connection-line { stroke: #4a90e2; stroke-width: 2; fill: none; }
        .feedback-line { stroke: #e74c3c; stroke-width: 1.5; fill: none; stroke-dasharray: 3,3; }
    </style>

    <!-- 定义箭头 -->
    <defs>
        <marker id="arrowhead" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
            <path d="M 0 0 L 10 5 L 0 10 z" fill="#333" />
        </marker>
        <marker id="blue-arrowhead" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
            <path d="M 0 0 L 10 5 L 0 10 z" fill="#4a90e2" />
        </marker>
        <marker id="red-arrowhead" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
            <path d="M 0 0 L 10 5 L 0 10 z" fill="#e74c3c" />
        </marker>
    </defs>

    <!-- 标题 -->
    <text x="600" y="40" class="title">智能网联汽车时空数据通用风险识别模型综合架构</text>

    <!-- 左侧: 实时信息输入 -->
    <rect x="20" y="80" width="260" height="680" class="box" fill="#f8f9fa"/>
    <text x="150" y="110" class="subtitle">实时信息输入</text>

    <!-- 数据源 -->
    <g>
        <rect x="40" y="140" width="220" height="120" class="box"/>
        <text x="150" y="160" class="box-title">数据源</text>
        <text x="60" y="190" class="icon">🚗</text>
        <text x="150" y="195" class="box-text">车端实时数据流</text>
        <text x="150" y="215" class="box-text">(轨迹、影像、点云、惯导)</text>
        <text x="60" y="240" class="icon">☁️</text>
        <text x="150" y="245" class="box-text">企业/云端业务数据流</text>
    </g>

    <!-- 数据接入与预处理 -->
    <g>
        <rect x="40" y="280" width="220" height="180" class="box"/>
        <text x="150" y="300" class="box-title">数据接入与预处理</text>
        <text x="150" y="330" class="box-text">1. 高性能协议解析 (Netty)</text>
        <text x="150" y="350" class="box-text">2. 身份认证与鉴权</text>
        <text x="150" y="370" class="box-text">3. 消息队列削峰填谷 (Kafka)</text>
        <text x="150" y="390" class="box-text">4. 数据清洗与标准化 (Flink)</text>
        <text x="150" y="410" class="box-text">- 格式转换、质量校验</text>
        <text x="150" y="430" class="box-text">- 时间窗口聚合</text>
    </g>

    <!-- 优化的垂直连线 -->
    <path d="M 150,260 L 150,280" class="arrow" />

    <g>
        <rect x="40" y="480" width="220" height="100" class="box"/>
        <text x="150" y="500" class="box-title">标准化事件流</text>
        <text x="150" y="530" class="box-text">结构化、规范化的</text>
        <text x="150" y="550" class="box-text">待分析数据事件</text>
    </g>

    <!-- 优化的垂直连线 -->
    <path d="M 150,460 L 150,480" class="arrow" />

    <!-- 中间: 模型技术矩阵 -->
    <rect x="300" y="80" width="580" height="680" class="box" fill="#f8f9fa"/>
    <text x="590" y="110" class="subtitle">模型技术矩阵与风险识别引擎</text>

    <!-- 优化的主要输入连线 -->
    <path d="M 260,530 L 300,530" class="connection-line" marker-end="url(#blue-arrowhead)" />
    <text x="270" y="520" class="flow-label">数据输入</text>

    <!-- 风险识别模型层 -->
    <g>
        <rect x="320" y="140" width="540" height="500" class="box"/>
        <text x="590" y="160" class="box-title">多维风险识别模型</text>

        <!-- 规则驱动模型 -->
        <rect x="340" y="180" width="240" height="140" class="box"/>
        <text x="460" y="200" class="box-title">1. 规则驱动模型</text>
        <text x="360" y="230" class="icon">📜</text>
        <text x="460" y="235" class="box-text">Drools 规则引擎</text>
        <text x="460" y="255" class="box-text">基于RETE算法的高效匹配</text>
        <text x="460" y="275" class="box-text">动态规则库(热部署)</text>
        <text x="460" y="295" class="box-text">业务场景: 明确违规行为</text>

        <!-- 时空行为分析模型 -->
        <rect x="600" y="180" width="240" height="140" class="box"/>
        <text x="720" y="200" class="box-title">2. 时空行为分析模型</text>
        <text x="620" y="230" class="icon">🗺️</text>
        <text x="720" y="235" class="box-text">地理围栏 (Geo-fencing)</text>
        <text x="720" y="255" class="box-text">轨迹聚类与异常检测</text>
        <text x="720" y="275" class="box-text">采集频率/精度分析</text>
        <text x="720" y="295" class="box-text">业务场景: 空间违规、异常聚集</text>
        
        <!-- 机器学习模型 -->
        <rect x="340" y="340" width="240" height="140" class="box"/>
        <text x="460" y="360" class="box-title">3. 机器学习模型</text>
        <text x="360" y="390" class="icon">🤖</text>
        <text x="460" y="395" class="box-text">行为基线建模</text>
        <text x="460" y="415" class="box-text">异常检测算法 (Isolation Forest)</text>
        <text x="460" y="435" class="box-text">多因子加权风险评分</text>
        <text x="460" y="455" class="box-text">业务场景: 未知风险、综合评估</text>

        <!-- 复杂事件处理 -->
        <rect x="600" y="340" width="240" height="140" class="box"/>
        <text x="720" y="360" class="box-title">4. 复杂事件处理 (CEP)</text>
        <text x="620" y="390" class="icon">⚡</text>
        <text x="720" y="395" class="box-text">Flink CEP 引擎</text>
        <text x="720" y="415" class="box-text">跨时空事件模式匹配</text>
        <text x="720" y="435" class="box-text">风险事件聚合与关联</text>
        <text x="720" y="455" class="box-text">业务场景: 复合型风险</text>
    </g>

    <!-- 风险识别引擎核心 -->
    <g>
        <rect x="320" y="530" width="540" height="100" class="box" fill="#e9ecef"/>
        <text x="590" y="550" class="box-title">风险识别引擎核心</text>
        <text x="450" y="580" class="box-text">模型协同决策</text>
        <path d="M 510,575 L 530,575" stroke="#4a90e2" stroke-width="2" marker-end="url(#blue-arrowhead)"/>
        <text x="590" y="580" class="box-text">风险判定与聚合</text>
        <path d="M 660,575 L 680,575" stroke="#4a90e2" stroke-width="2" marker-end="url(#blue-arrowhead)"/>
        <text x="740" y="580" class="box-text">风险事件生成</text>
    </g>

    <!-- 优化的模型间连线 - 避免遮挡 -->
    <!-- 从输入到规则驱动模型 -->
    <path d="M 320,530 L 300,530 L 300,250 L 340,250" class="dashed-arrow" />

    <!-- 从输入到时空行为分析模型 -->
    <path d="M 320,530 L 300,530 L 300,200 L 580,200 L 580,250 L 600,250" class="dashed-arrow" />

    <!-- 从输入到机器学习模型 -->
    <path d="M 320,530 L 300,530 L 300,410 L 340,410" class="dashed-arrow" />

    <!-- 从输入到复杂事件处理 -->
    <path d="M 320,530 L 300,530 L 300,360 L 580,360 L 580,410 L 600,410" class="dashed-arrow" />

    <!-- 从模型到引擎核心的连线 -->
    <path d="M 460,480 L 460,530" class="connection-line" marker-end="url(#blue-arrowhead)" />
    <path d="M 720,480 L 720,530" class="connection-line" marker-end="url(#blue-arrowhead)" />
    
    <!-- 右侧: 风险事件输出 -->
    <rect x="900" y="80" width="280" height="680" class="box" fill="#f8f9fa"/>
    <text x="1040" y="110" class="subtitle">风险事件输出</text>

    <!-- 优化的主要输出连线 -->
    <path d="M 860,580 L 900,580" class="connection-line" marker-end="url(#blue-arrowhead)" />
    <text x="870" y="570" class="flow-label">风险输出</text>

    <!-- 风险事件分级 -->
    <g>
        <rect x="920" y="140" width="240" height="160" class="box"/>
        <text x="1040" y="160" class="box-title">1. 风险事件分级</text>
        <text x="1040" y="190" class="box-text" fill="red">特别重大风险</text>
        <text x="1040" y="210" class="box-text" fill="orange">重大风险</text>
        <text x="1040" y="230" class="box-text" fill="#e67e22">较大风险</text>
        <text x="1040" y="250" class="box-text" fill="#f1c40f">一般风险</text>
        <text x="1040" y="270" class="box-text" fill="#2ecc71">轻微风险</text>
    </g>

    <!-- 优化的垂直连线 -->
    <path d="M 1040,300 L 1040,320" class="connection-line" marker-end="url(#blue-arrowhead)" />

    <!-- 监测预警与处置联动 -->
    <g>
        <rect x="920" y="320" width="240" height="160" class="box"/>
        <text x="1040" y="340" class="box-title">2. 监测预警与处置联动</text>
        <text x="940" y="370" class="icon">🔔</text>
        <text x="1040" y="375" class="box-text">多通道预警 (短信, 邮件, APP)</text>
        <text x="1040" y="395" class="box-text">预警升级机制</text>
        <text x="940" y="430" class="icon">⚙️</text>
        <text x="1040" y="435" class="box-text">自动化处置 (API接口联动)</text>
        <text x="1040" y="455" class="box-text">人机协同处置 (工单系统)</text>
    </g>

    <!-- 优化的垂直连线 -->
    <path d="M 1040,480 L 1040,500" class="connection-line" marker-end="url(#blue-arrowhead)" />

    <!-- 可视化与溯源分析 -->
    <g>
        <rect x="920" y="500" width="240" height="160" class="box"/>
        <text x="1040" y="520" class="box-title">3. 可视化与溯源分析</text>
        <text x="940" y="550" class="icon">📊</text>
        <text x="1040" y="555" class="box-text">风险态势可视化大屏</text>
        <text x="1040" y="575" class="box-text">生成监测与合规报表</text>
        <text x="940" y="610" class="icon">🔍</text>
        <text x="1040" y="615" class="box-text">数据血缘追踪</text>
        <text x="1040" y="635" class="box-text">事件回溯与责任认定</text>
    </g>

    <!-- 添加反馈连线 -->
    <path d="M 920,220 L 880,220 L 880,680 L 300,680 L 300,600 L 320,600"
          class="feedback-line" marker-end="url(#red-arrowhead)" />
    <text x="600" y="695" class="flow-label" fill="#e74c3c">反馈优化</text>

</svg>
